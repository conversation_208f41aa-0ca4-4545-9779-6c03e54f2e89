<template>
	<view class="voice-record-modal" v-if="isVisible">
		<!-- 遮罩层 -->
		<view class="modal-mask"></view>
		
		<!-- 录音内容区域 -->
		<view class="modal-content">
			<!-- 语音气泡 -->
			<view class="voice-bubble" :class="{'recording': formData.isRecording}">
				<!-- 音频波形动画 -->
				<view class="voice-wave">
					<view class="wave-bar" v-for="(item, index) in waveData" :key="index" 
						:style="{height: item + 'rpx', animationDelay: (index * 0.1) + 's'}"
						:class="{'active': formData.isRecording}">
					</view>
				</view>
				<!-- 录音时长 -->
				<view class="voice-time" v-if="formData.isRecording">{{formData.showTime || '00:00'}}</view>
			</view>
			
			<!-- 提示文字 -->
			<view class="voice-tip" v-if="formData.isRecording">松开发送</view>

			<!-- 底部按钮区域 -->
			<view class="bottom-buttons">
				<!-- 取消按钮 -->
				<view class="btn-cancel" :class="{'active': isOverCancel}">
					<view class="btn-icon">✕</view>
				</view>

				<!-- 发送按钮 -->
				<!-- <view class="btn-send">
					<view class="btn-icon">文</view>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue'

// 定义事件
const emit = defineEmits(['cbResult'])

// 响应式数据
const isVisible = ref(false)
const recorderManager = ref<any>(null)
const timekeepingObj = ref<any>(null)
const touchObj = ref<any>(null)
const isOverCancel = ref(false) // 是否滑动到取消按钮上

// 录音相关数据
const formData = reactive({
	isRecording: false,
	tempFilePath: '',
	showTime: '',
	duration: 0,
	isPlay: false,
	tempFile: null,
	isAuthorized: true
})

// 音频波形数据
const waveData = ref([20, 35, 45, 25, 55, 30, 40, 50, 25, 35, 45, 30, 40, 25, 35])

// 初始化录音管理器
const initRecorderManager = () => {
	log
	recorderManager.value = uni.getRecorderManager()
	recorderManager.value.onStop((res: any) => {
		log('录音结束', res)
		if (!formData.isAuthorized) return
		formData.isRecording = false
		formData.tempFile = res
		formData.tempFilePath = res.tempFilePath
		if(isOverCancel.value) {
			clearFormData()
		}else{
			if(formData.tempFilePath && formData.duration > 0){
				emit('cbResult', formData)
				hideVoice()
			}else{
				console.log('录音不存在或小于1s');
			}
		}
	})
}

// 清空表单数据
const clearFormData = () => {
	formData.isRecording = false
	formData.tempFilePath = ''
	formData.showTime = ''
	formData.duration = 0
	formData.isPlay = false
	formData.tempFile = null
	formData.isAuthorized = true
	isOverCancel.value = false
}

// 显示录音弹窗
const showVoice = () => {
	isVisible.value = true
	clearFormData()
	nextTick(() => {
		initRecorderManager()
		// 自动开始录音
		doStartRecord()
	})
}

// 隐藏录音弹窗
const hideVoice = () => {
	isVisible.value = false
}

// 开始录音
const doStartRecord = async (e?: any) => {
	// #ifdef H5
	uni.showToast({
		icon: 'none',
		title: '暂不支持录音'
	})
	return
	// #endif
	
	// #ifndef H5
	clearFormData()
	
	try {
		// 检查权限
		// #ifdef APP || MP-WEIXIN
		const authResult = await uni.getAppAuthorizeSetting()
		const getAuthorized = authResult.microphoneAuthorized
		if (uni.getSystemInfoSync().platform === 'ios') {
			if (getAuthorized === 'denied') {
				formData.isAuthorized = false
				return uni.showModal({
					title: '提示',
					content: '是否要开启麦克风权限？',
					success(res) {
						if (res.confirm) {
							uni.openAppAuthorizeSetting()
						}
					}
				})
			}
		} else if (getAuthorized !== 'authorized') {
			formData.isAuthorized = false
			return uni.showToast({
				icon: 'none',
				duration: 3000,
				title: '请允许使用或在系统设置中打开麦克风权限'
			})
		}
		// #endif
		
		// 开始录音
		recorderManager.value.start({ duration: 60000 })
		formData.isRecording = true
		doStartTimekeeping()
	} catch (error) {
		console.error('录音启动失败:', error)
		uni.showToast({
			icon: 'none',
			title: '录音启动失败'
		})
	}
	// #endif
}

// 结束录音
const doEndRecord = async () => {
	clearTimeout(touchObj.value)
	doEndTimekeeping()
	if (recorderManager.value) {
		recorderManager.value.stop()
	}
	formData.isRecording = false
}

// 开始计时
const doStartTimekeeping = () => {
	doEndTimekeeping()
	let seconds = 0
	timekeepingObj.value = setInterval(() => {
		seconds += 1
		formData.duration = seconds
		formData.showTime = formatTime(seconds)
		if (seconds >= 60) {
			doEndRecord()
		}
	}, 1000)
}

// 结束计时
const doEndTimekeeping = () => {
	if (timekeepingObj.value) {
		clearInterval(timekeepingObj.value)
		timekeepingObj.value = null
	}
}

// 格式化时间
const formatTime = (time: number) => {
	const minute = Math.floor(time / 60)
	const second = time % 60
	return `${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`
}
// 获取屏幕信息
const systemInfo = uni.getSystemInfoSync()
const windowHeight = systemInfo.windowHeight
const windowWidth = systemInfo.windowWidth
// 触摸移动
const onTouchMove = (e: any) => {
	if (!formData.isRecording) return

	// 获取触摸点坐标
	const touch = e.touches[0]
	const x = touch.clientX
	const y = touch.clientY

	// 根据设计，取消按钮在底部左侧
	// 按钮大小为120rpx，位置在bottom: 200rpx, 左侧padding: 100rpx
	// 转换rpx到px: 1rpx = windowWidth / 750
	const rpxToPx = windowWidth / 750
	const buttonSize = 120 * rpxToPx
	const bottomDistance = 200 * rpxToPx
	const leftPadding = 100 * rpxToPx

	// 取消按钮区域
	const cancelButtonArea = {
		left: leftPadding - buttonSize / 2,
		right: leftPadding + buttonSize / 2,
		top: windowHeight - bottomDistance - buttonSize / 2,
		bottom: windowHeight - bottomDistance + buttonSize / 2
	}

	const isInCancelArea = x >= cancelButtonArea.left &&
						   x <= cancelButtonArea.right &&
						   y >= cancelButtonArea.top &&
						   y <= cancelButtonArea.bottom

	if (isInCancelArea && !isOverCancel.value) {
		// 进入取消区域
		isOverCancel.value = true
		// 取消录音
		doEndRecord()
		clearFormData()
		// 震动反馈
		// #ifndef H5
		try {
			uni.vibrateShort({})
		} catch (error) {}
		// #endif
	} else if (!isInCancelArea && isOverCancel.value) {
		// 离开取消区域，但录音已经被取消了，不能重新开始
		isOverCancel.value = false
	}
}

// 触摸结束
const onTouchEnd = () => {
	doEndRecord()
}

// 暴露方法给父组件
defineExpose({
	showVoice,
	hideVoice,
	onTouchMove,
	onTouchEnd
})
</script>

<style lang="less" scoped>
.voice-record-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
	position: relative;
	z-index: 10;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	height: 100%;
	justify-content: center;
}

.voice-bubble {
	width: 400rpx;
	height: 200rpx;
	background: linear-gradient(135deg, #7ED321, #50E3C2);
	border-radius: 20rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(126, 211, 33, 0.3);
	
	&::after {
		content: '';
		position: absolute;
		bottom: -20rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 0;
		border-left: 20rpx solid transparent;
		border-right: 20rpx solid transparent;
		border-top: 20rpx solid #50E3C2;
	}
	
	&.recording {
		animation: bubble-pulse 1.5s ease-in-out infinite;
	}
}

.voice-wave {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 4rpx;
	margin-bottom: 10rpx;
}

.wave-bar {
	width: 6rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 3rpx;
	transition: height 0.3s ease;
	
	&.active {
		animation: wave-animation 1.2s ease-in-out infinite;
	}
}

.voice-time {
	font-size: 24rpx;
	color: #fff;
	font-weight: bold;
}

.voice-tip {
	font-size: 28rpx;
	color: #fff;
	margin-bottom: 100rpx;
	text-align: center;
	
	&.recording-tip {
		color: #FFD700;
	}
}

.bottom-buttons {
	position: absolute;
	bottom: 200rpx;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 0 100rpx;
}

.btn-cancel, .btn-send {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;

	.btn-icon {
		font-size: 40rpx;
		color: #fff;
		font-weight: bold;
	}

	&.disabled {
		opacity: 0.5;
	}

	&.active {
		background: rgba(255, 0, 0, 0.8);
		transform: scale(1.2);
		box-shadow: 0 0 20rpx rgba(255, 0, 0, 0.6);
	}
}



@keyframes bubble-pulse {
	0%, 100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
}

@keyframes wave-animation {
	0%, 100% {
		transform: scaleY(1);
	}
	50% {
		transform: scaleY(2);
	}
}


</style>
